mode: train
seed: 42
output_dir: ./output
data:
  train_features_path: ./data/cleaned/training.csv
  train_labels_path: ./data/cleaned/training_labels.csv
  val_features_path: ./data/cleaned/validation.csv
  val_labels_path: ./data/cleaned/validation_labels.csv
model:
  num_layers: 4
  hidden_size:
  - 128
  - 96
  - 64
  - 32
  activation: relu
  input_dim: 2808
  output_dim: 1
train:
  epochs: 10
  batch_size: 64
  learning_rate: 0.001
  eval_during_train: true
  eval_freq: 1
  save_freq: 5
  lr_scheduler:
    gamma: 0.95
    decay_steps: 5
  warmup_epoch: 2
  warmup_start_lr: 1.0e-06
eval:
  batch_size: 64
  eval_with_no_grad: true
  pretrained_model_path: null
