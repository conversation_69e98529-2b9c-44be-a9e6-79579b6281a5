mode: train
output_dir: ./output
log_freq: 20
seed: 42
use_vdl: false
use_tbd: false
wandb_config: null
use_wandb: false
device: gpu
use_amp: false
amp_level: O1
to_static: false
prim: false
log_level: info
TRAIN:
  epochs: 10
  iters_per_epoch: 20
  update_freq: 1
  save_freq: 10
  eval_during_train: true
  start_eval_epoch: 1
  eval_freq: 5
  checkpoint_path: null
  pretrained_model_path: null
  ema:
    use_ema: false
    decay: 0.9
    avg_freq: 1
  swa:
    use_swa: false
    avg_freq: 1
    avg_range: null
  batch_size: 64
  learning_rate: 0.001
  log_freq: 50
  lr_scheduler:
    gamma: 0.95
    decay_steps: 5
  warmup_epoch: 2
  warmup_start_lr: 1.0e-06
EVAL:
  pretrained_model_path: null
  eval_with_no_grad: false
  compute_metric_by_batch: false
  batch_size: 256
INFER:
  pretrained_model_path: null
  export_path: ./inference
  pdmodel_path: null
  pdiparams_path: null
  onnx_path: null
  device: cpu
  engine: native
  precision: fp32
  ir_optim: true
  min_subgraph_size: 30
  gpu_mem: 2000
  gpu_id: 0
  max_batch_size: 1024
  num_cpu_threads: 10
  batch_size: 256
data:
  train_features_path: ./data/cleaned/training.csv
  train_labels_path: ./data/cleaned/training_labels.csv
  val_features_path: ./data/cleaned/validation.csv
  val_labels_path: ./data/cleaned/validation_labels.csv
model:
  num_layers: 4
  hidden_size:
  - 128
  - 96
  - 64
  - 32
  activation: relu
  input_dim: 2808
  output_dim: 1
eval:
  batch_size: 64
  eval_with_no_grad: true
  pretrained_model_path: null
  log_freq: 50
