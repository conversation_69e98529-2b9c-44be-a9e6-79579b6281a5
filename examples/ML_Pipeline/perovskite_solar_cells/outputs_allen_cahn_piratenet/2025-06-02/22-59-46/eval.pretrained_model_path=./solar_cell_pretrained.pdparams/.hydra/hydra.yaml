hydra:
  run:
    dir: outputs_allen_cahn_piratenet/${now:%Y-%m-%d}/${now:%H-%M-%S}/${hydra.job.override_dirname}
  sweep:
    dir: ${hydra.run.dir}
    subdir: ./
  launcher:
    _target_: hydra._internal.core_plugins.basic_launcher.BasicLauncher
  sweeper:
    _target_: hydra._internal.core_plugins.basic_sweeper.BasicSweeper
    max_batch_size: null
    params: null
  help:
    app_name: ${hydra.job.name}
    header: '${hydra.help.app_name} is powered by Hydra.

      '
    footer: 'Powered by Hydra (https://hydra.cc)

      Use --hydra-help to view Hydra specific help

      '
    template: '${hydra.help.header}

      == Configuration groups ==

      Compose your configuration from those groups (group=option)


      $APP_CONFIG_GROUPS


      == Config ==

      Override anything in the config (foo.bar=value)


      $CONFIG


      ${hydra.help.footer}

      '
  hydra_help:
    template: 'Hydra (${hydra.runtime.version})

      See https://hydra.cc for more info.


      == Flags ==

      $FLAGS_HELP


      == Configuration groups ==

      Compose your configuration from those groups (For example, append hydra/job_logging=disabled
      to command line)


      $HYDRA_CONFIG_GROUPS


      Use ''--cfg hydra'' to Show the Hydra config.

      '
    hydra_help: ???
  hydra_logging:
    version: 1
    formatters:
      simple:
        format: '[%(asctime)s][HYDRA] %(message)s'
    handlers:
      console:
        class: logging.StreamHandler
        formatter: simple
        stream: ext://sys.stdout
    root:
      level: INFO
      handlers:
      - console
    loggers:
      logging_example:
        level: DEBUG
    disable_existing_loggers: false
  job_logging:
    version: 1
    formatters:
      simple:
        format: '[%(asctime)s][%(name)s][%(levelname)s] - %(message)s'
    handlers:
      console:
        class: logging.StreamHandler
        formatter: simple
        stream: ext://sys.stdout
      file:
        class: logging.FileHandler
        formatter: simple
        filename: ${hydra.runtime.output_dir}/${hydra.job.name}.log
    root:
      level: INFO
      handlers:
      - console
      - file
    disable_existing_loggers: false
  env: {}
  mode: RUN
  searchpath: []
  callbacks:
    init_callback:
      _target_: ppsci.utils.callbacks.InitCallback
  output_subdir: .hydra
  overrides:
    hydra:
    - hydra.mode=RUN
    task:
    - mode=eval
    - eval.pretrained_model_path=./solar_cell_pretrained.pdparams
  job:
    name: ${mode}
    chdir: false
    override_dirname: eval.pretrained_model_path=./solar_cell_pretrained.pdparams
    id: ???
    num: ???
    config_name: psc_nn.yaml
    env_set: {}
    env_copy: []
    config:
      override_dirname:
        kv_sep: '='
        item_sep: ','
        exclude_keys:
        - mode
        - output_dir
        - log_freq
        - seed
        - use_vdl
        - use_tbd
        - wandb_config
        - use_wandb
        - device
        - use_amp
        - amp_level
        - to_static
        - prim
        - log_level
        - TRAIN.save_freq
        - TRAIN.eval_during_train
        - TRAIN.start_eval_epoch
        - TRAIN.eval_freq
        - TRAIN.checkpoint_path
        - TRAIN.pretrained_model_path
        - EVAL.pretrained_model_path
        - EVAL.eval_with_no_grad
        - EVAL.compute_metric_by_batch
        - EVAL.batch_size
        - INFER.pretrained_model_path
        - INFER.export_path
        - INFER.pdmodel_path
        - INFER.pdiparams_path
        - INFER.onnx_path
        - INFER.device
        - INFER.engine
        - INFER.precision
        - INFER.ir_optim
        - INFER.min_subgraph_size
        - INFER.gpu_mem
        - INFER.gpu_id
        - INFER.max_batch_size
        - INFER.num_cpu_threads
        - INFER.batch_size
  runtime:
    version: 1.3.2
    version_base: '1.3'
    cwd: /home/<USER>/ai4s/ML_Pipeline/PaddleScience/examples/ML_Pipeline/perovskite_solar_cells
    config_sources:
    - path: hydra.conf
      schema: pkg
      provider: hydra
    - path: /home/<USER>/ai4s/ML_Pipeline/PaddleScience/examples/ML_Pipeline/perovskite_solar_cells/conf
      schema: file
      provider: main
    - path: ''
      schema: structured
      provider: schema
    output_dir: /home/<USER>/ai4s/ML_Pipeline/PaddleScience/examples/ML_Pipeline/perovskite_solar_cells/outputs_allen_cahn_piratenet/2025-06-02/22-59-46/eval.pretrained_model_path=./solar_cell_pretrained.pdparams
    choices:
      hydra/job/config/override_dirname/exclude_keys: exclude_keys_default
      INFER: infer_default
      EVAL: eval_default
      TRAIN/swa: swa_default
      TRAIN/ema: ema_default
      TRAIN: train_default
      hydra/env: default
      hydra/callbacks: null
      hydra/job_logging: default
      hydra/hydra_logging: default
      hydra/hydra_help: default
      hydra/help: default
      hydra/sweeper: basic
      hydra/launcher: basic
      hydra/output: default
  verbose: false
