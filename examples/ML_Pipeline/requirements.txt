# This file may be used to create an environment using:
# $ conda create --name <env> --file <this file>
# platform: linux-64
# created-by: conda 24.9.2
_libgcc_mutex=0.1=conda_forge
_openmp_mutex=4.5=2_gnu
addict=2.4.0=pypi_0
alembic=1.14.0=pypi_0
annotated-types=0.7.0=pypi_0
antlr4-python3-runtime=4.9.3=pypi_0
anyio=4.6.2.post1=pyhd8ed1ab_0
astor=0.8.1=pyh9f0ad1d_0
asttokens=2.4.1=pypi_0
attrs=24.2.0=pypi_0
babel=2.16.0=pypi_0
blinker=1.9.0=pypi_0
bracex=2.5.post1=pypi_0
bzip2=1.0.8=h4bc722e_7
ca-certificates=2024.8.30=hbcca054_0
certifi=2024.8.30=pyhd8ed1ab_0
cfgv=3.4.0=pypi_0
charset-normalizer=3.4.0=pypi_0
click=8.1.7=pypi_0
colorama=0.4.6=pypi_0
colorlog=6.9.0=pypi_0
comm=0.2.2=pypi_0
configargparse=1.7=pypi_0
contourpy=1.3.0=pypi_0
cuda-cudart=11.8.89=0
cuda-cupti=11.8.87=0
cuda-libraries=11.8.0=0
cuda-nvrtc=11.8.89=0
cuda-nvtx=11.8.86=0
cuda-runtime=11.8.0=0
cuda-version=12.6=3
cudnn=********=h092f7fd_3
cycler=0.12.1=pypi_0
dash=2.18.2=pypi_0
dash-core-components=2.0.0=pypi_0
dash-html-components=2.0.0=pypi_0
dash-table=5.0.0=pypi_0
decorator=5.1.1=pyhd8ed1ab_0
distlib=0.3.9=pypi_0
einops=0.8.0=pypi_0
exceptiongroup=1.2.2=pyhd8ed1ab_0
executing=2.1.0=pypi_0
fastjsonschema=2.20.0=pypi_0
filelock=3.16.1=pypi_0
flask=3.0.3=pypi_0
fonttools=4.55.0=pypi_0
freetype=2.12.1=h267a509_2
ghp-import=2.1.0=pypi_0
gitdb=4.0.11=pypi_0
gitpython=3.1.43=pypi_0
greenlet=3.1.1=pypi_0
griffe=1.5.1=pypi_0
h11=0.14.0=pyhd8ed1ab_0
h2=4.1.0=pyhd8ed1ab_0
h5py=3.12.1=pypi_0
hpack=4.0.0=pyh9f0ad1d_0
httpcore=1.0.7=pyh29332c3_1
httpx=0.27.2=pyhd8ed1ab_0
hydra-core=1.3.2=pypi_0
hyperframe=6.0.1=pyhd8ed1ab_0
identify=2.6.2=pypi_0
idna=3.10=pyhd8ed1ab_0
imageio=2.36.0=pypi_0
importlib-metadata=8.5.0=pypi_0
importlib-resources=6.4.5=pypi_0
ipython=8.18.1=pypi_0
ipywidgets=8.1.5=pypi_0
itsdangerous=2.2.0=pypi_0
jedi=0.19.2=pypi_0
jinja2=3.1.4=pypi_0
joblib=1.4.2=pypi_0
jsonschema=4.23.0=pypi_0
jsonschema-specifications=2024.10.1=pypi_0
jupyter-core=5.7.2=pypi_0
jupyterlab-widgets=3.0.13=pypi_0
kiwisolver=1.4.7=pypi_0
lcms2=2.16=hb7c19ff_0
ld_impl_linux-64=2.43=h712a8e2_2
lerc=4.0.0=h27087fc_0
libblas=3.9.0=25_linux64_openblas
libcblas=3.9.0=25_linux64_openblas
libcublas=*********=0
libcufft=*********=0
libcufile=********=0
libcurand=*********=0
libcusolver=*********=0
libcusparse=*********=0
libdeflate=1.22=hb9d3cd8_0
libffi=3.4.2=h7f98852_5
libgcc=14.2.0=h77fa898_1
libgcc-ng=14.2.0=h69a702a_1
libgfortran=14.2.0=h69a702a_1
libgfortran5=14.2.0=hd5240d6_1
libgomp=14.2.0=h77fa898_1
libjpeg-turbo=3.0.0=hd590300_1
liblapack=3.9.0=25_linux64_openblas
libnpp=*********=0
libnsl=2.0.1=hd590300_0
libnvjpeg=*********=0
libopenblas=0.3.28=pthreads_h94d23a6_1
libpng=1.6.44=hadc24fc_0
libsqlite=3.47.0=hadc24fc_1
libstdcxx=14.2.0=hc0a3c3a_1
libstdcxx-ng=14.2.0=h4852527_1
libtiff=4.7.0=he137b08_1
libuuid=2.38.1=h0b41bf4_0
libwebp-base=1.4.0=hd590300_0
libxcb=1.17.0=h8a09558_0
libxcrypt=4.4.36=hd590300_1
libzlib=1.3.1=hb9d3cd8_2
lxml=5.3.0=pypi_0
mako=1.3.7=pypi_0
markdown=3.7=pypi_0
markdown-it-py=3.0.0=pypi_0
markupsafe=3.0.2=pypi_0
matplotlib=3.9.2=pypi_0
matplotlib-inline=0.1.7=pypi_0
mdurl=0.1.2=pypi_0
mergedeep=1.3.4=pypi_0
meshio=5.3.4=pypi_0
mike=2.1.3=pypi_0
mkdocs=1.6.1=pypi_0
mkdocs-autorefs=1.2.0=pypi_0
mkdocs-get-deps=0.2.0=pypi_0
mkdocs-git-revision-date-localized-plugin=1.3.0=pypi_0
mkdocs-glightbox=0.4.0=pypi_0
mkdocs-include-markdown-plugin=7.1.2=pypi_0
mkdocs-material=9.5.49=pypi_0
mkdocs-material-extensions=1.3.1=pypi_0
mkdocs-video=1.5.0=pypi_0
mkdocstrings=0.27.0=pypi_0
mkdocstrings-python=1.12.2=pypi_0
mpmath=1.3.0=pypi_0
nbformat=5.10.4=pypi_0
nccl=2.23.4.1=h52f6c39_2
ncurses=6.5=he02047a_1
nest-asyncio=1.6.0=pypi_0
networkx=3.2.1=pyhd8ed1ab_0
nodeenv=1.9.1=pypi_0
numpy=1.26.4=pypi_0
nvidia-cudnn-cu11=********=pypi_0
omegaconf=2.3.0=pypi_0
open3d=0.18.0=pypi_0
openjpeg=2.5.2=h488ebb8_0
openssl=3.4.0=hb9d3cd8_0
opt_einsum=3.3.0=pyhc1e730c_2
optuna=4.1.0=pypi_0
packaging=24.2=pypi_0
paddlepaddle-cuda=11.8=many_linux
paddlepaddle-gpu=3.0.0b2=py39_gpu_cuda11.8_many_linux
paddlesci=0.0.post1383+g4857525.d20241226=pypi_0
paginate=0.5.7=pypi_0
pandas=2.2.3=pypi_0
parso=0.8.4=pypi_0
pathspec=0.12.1=pypi_0
pexpect=4.9.0=pypi_0
pillow=11.0.0=py39h538c539_0
pip=24.3.1=pyh8b19718_0
platformdirs=4.3.6=pypi_0
plotly=5.24.1=pypi_0
pre-commit=4.0.1=pypi_0
prompt-toolkit=3.0.48=pypi_0
protobuf=5.28.3=py39hf88036b_0
pthread-stubs=0.4=hb9d3cd8_1002
ptyprocess=0.7.0=pypi_0
pure-eval=0.2.3=pypi_0
pyaml=24.9.0=pypi_0
pybind11=2.13.6=pypi_0
pydantic=2.10.1=pypi_0
pydantic-core=2.27.1=pypi_0
pyevtk=1.6.0=pypi_0
pygments=2.18.0=pypi_0
pymdown-extensions=10.12=pypi_0
pymesh=1.0.2=pypi_0
pyparsing=3.2.0=pypi_0
pyquaternion=0.9.9=pypi_0
pysdf=0.1.9=pypi_0
python=3.9.20=h13acc7a_1_cpython
python-dateutil=2.9.0.post0=pypi_0
python_abi=3.9=5_cp39
pytz=2024.2=pypi_0
pyyaml=6.0.2=pypi_0
pyyaml-env-tag=0.1=pypi_0
readline=8.2=h8228510_1
referencing=0.35.1=pypi_0
regex=2024.11.6=pypi_0
requests=2.32.3=pypi_0
retrying=1.3.4=pypi_0
rich=13.9.4=pypi_0
rpds-py=0.21.0=pypi_0
scikit-learn=1.4.2=pypi_0
scikit-optimize=0.10.2=pypi_0
scipy=1.13.1=pypi_0
seaborn=0.13.2=pypi_0
setuptools=75.6.0=pyhff2d567_0
six=1.16.0=pypi_0
smmap=5.0.1=pypi_0
sniffio=1.3.1=pyhd8ed1ab_0
sqlalchemy=2.0.36=pypi_0
stack-data=0.6.3=pypi_0
sympy=1.13.3=pypi_0
tenacity=9.0.0=pypi_0
threadpoolctl=3.5.0=pypi_0
tk=8.6.13=noxft_h4845f30_101
tqdm=4.67.0=pypi_0
traitlets=5.14.3=pypi_0
typing_extensions=4.12.2=pyha770c72_0
tzdata=2024.2=pypi_0
urllib3=2.2.3=pypi_0
verspec=0.1.0=pypi_0
virtualenv=20.27.1=pypi_0
watchdog=6.0.0=pypi_0
wcmatch=10.0=pypi_0
wcwidth=0.2.13=pypi_0
werkzeug=3.0.6=pypi_0
wget=3.2=pypi_0
wheel=0.45.0=pyhd8ed1ab_0
widgetsnbextension=4.0.13=pypi_0
xorg-libxau=1.0.11=hb9d3cd8_1
xorg-libxdmcp=1.1.5=hb9d3cd8_0
xz=5.2.6=h166bdaf_0
zipp=3.21.0=pypi_0
zstd=1.5.6=ha6fb4c9_0
